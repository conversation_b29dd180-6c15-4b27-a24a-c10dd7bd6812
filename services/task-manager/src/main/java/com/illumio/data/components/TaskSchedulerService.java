package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.*;
import com.illumio.data.model.mapper.TenantOnboardingMapper;
import com.illumio.data.repositories.OnboardingRepository;
import com.illumio.data.util.CommonUtil;
import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import static com.illumio.data.model.TaskStatusType.AWAITING_ASYNC;
import static com.illumio.data.model.TaskStatusType.CHECKING_STATUS_ASYNC;
import static com.illumio.data.model.TaskStatusType.SCHEDULED;
import static com.illumio.data.model.TaskType.GREENFIELD;
import static com.illumio.data.util.TaskManagerConstants.COMPLETED_TASK_STATUSES;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskSchedulerService {

    private final OnboardingRepository onboardingRepository;
    private final TaskDbService taskDbService;
    private final TaskSenderService taskSenderService;
    private final LDService ldService;
    private final TaskManagerConfiguration taskManagerConfiguration;
    private final ObjectMapper objectMapper;

    @LogReactiveExecutionTime
    @Scheduled(fixedRateString = "#{@taskManagerConfiguration.getScheduleTasksConfig().getDeltaSyncScheduleDuration().toMillis()}")
    public Mono<Void> scheduleTasksIfEligible() {
        return onboardingRepository.findAll()
                                   .map(tenantOnboardingEntity -> TenantOnboardingMapper.toTenantOnboarding(
                                           tenantOnboardingEntity, objectMapper))
                                   .flatMap(this::checkAndCreateTask)
                                   .then();
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    private Mono<Void> checkAndCreateTask(TenantOnboarding onboarding) {
        return ldService.integrationIsEnabledForTenant(onboarding.getTenantId(), onboarding.getIntegration())
                        .filter(Boolean::booleanValue)
                        .then(taskDbService.findLatestTask(onboarding))
                        .flatMap(latestTask -> {
                            if (canScheduleNewTask(latestTask, onboarding)) {
                                return createNewTask(onboarding);
                            } else if (canProgressTask(latestTask)) {
                                return getTaskProgression(latestTask);
                            }
                            else {
                                log.debug("Waiting for sync task with status={} of taskType={} to complete for tenant {} and integration {}.",
                                        latestTask.getStatusType(),
                                        latestTask.getTaskType(),
                                        onboarding.getTenantId(),
                                        onboarding.getIntegration());
                                return Mono.empty();
                            }
                        })
                        .flatMap(newTaskStatus -> scheduleTask(newTaskStatus, onboarding));
    }

    private Mono<Void> scheduleTask(final TaskStatus taskStatus, final TenantOnboarding tenantOnboarding) {
        return Mono.just(taskStatus)
                   .flatMap(unsavedTaskUpdates ->
                           createSyncTask(unsavedTaskUpdates, tenantOnboarding)
                                   .flatMap(taskSenderService::sendTask)
                                   .thenReturn(unsavedTaskUpdates))
                   .flatMap(taskDbService::updateTaskStatus)
                   .onErrorResume(e -> {
                       log.error("Error scheduling next state for task with tenantId and integration: {}, {}",
                               taskStatus.getTenantId(), taskStatus.getIntegration(), e);
                       return Mono.empty();
                   })
                   .then();
    }

    private boolean canScheduleNewTask(final TaskStatus latestTaskStatus, final TenantOnboarding onboarding) {
        if (latestTaskStatus == null) {
            log.debug("Greenfield didn't finish for tenant {} and integration {}. Cannot create a new delta sync task.",
                    onboarding.getTenantId(),
                    onboarding.getIntegration());
            return false;
        }

        final TaskStatusType latestStatusType = latestTaskStatus.getStatusType();

        return COMPLETED_TASK_STATUSES.contains(latestStatusType.toString());
    }

    private Mono<TaskStatus> createNewTask(TenantOnboarding onboarding) {
        log.debug("Creating new task for tenant {} and integration {}.",
                onboarding.getTenantId(),
                onboarding.getIntegration());

        final String scheduledTime = DateTimeUtil.generateCurrentTimestamp();
        final Integration integration = onboarding.getIntegration();
        final String tenantId = onboarding.getTenantId();
        return taskDbService.determineTaskType(onboarding)
                            .map(taskType ->
                                    TaskStatus.builder()
                                              .taskId(Util.generateNewTaskId(tenantId, integration, scheduledTime))
                                              .integration(integration)
                                              .tenantId(tenantId)
                                              .statusUpdateTime(scheduledTime)
                                              .statusType(GREENFIELD.equals(taskType)
                                                      ? TaskStatusType.getInitialTaskStatus(integration) : SCHEDULED)
                                              .taskType(taskType)
                                              .build());
    }

    private boolean canProgressTask(final TaskStatus taskStatus) {
        return taskStatus.getStatusType().equals(AWAITING_ASYNC)
                || taskStatus.getStatusType().equals(TaskStatusType.READY_ASYNC);
    }

    private Mono<TaskStatus> getTaskProgression(final TaskStatus taskStatus) {
        return switch (taskStatus.getStatusType()) {
            case AWAITING_ASYNC -> Mono.delay(taskManagerConfiguration.getScheduleTasksConfig()
                                                                      .getAsyncTaskReadinessCheckDelay())
                                       .then(Mono.just(taskStatus.toBuilder()
                                                                 .statusType(CHECKING_STATUS_ASYNC)
                                                                 .build()));
            case READY_ASYNC -> Mono.just(taskStatus.toBuilder()
                                                    .statusType(SCHEDULED)
                                                    .build());
            default -> Mono.just(taskStatus);
        };
    }

    public Mono<Void> progressTask(final TaskStatus taskStatus) {
        if (!canProgressTask(taskStatus)) {
            return Mono.empty();
        }

        return getTaskProgression(taskStatus).flatMap(newTaskStatus ->
                        onboardingRepository.findByTenantIdAndIntegration(newTaskStatus.getTenantId(), newTaskStatus.getIntegration().name())
                                            .map(tenantOnboardingEntity -> TenantOnboardingMapper.toTenantOnboarding(
                                                    tenantOnboardingEntity, objectMapper))
                                            .flatMap(tenantOnboarding -> scheduleTask(newTaskStatus, tenantOnboarding)));
    }

    private Mono<SyncTask> createSyncTask(TaskStatus newTaskStatus, TenantOnboarding onboarding) {
        return taskDbService.findLastSuccessfulTask(onboarding)
                .map(lastSuccessfulTask -> buildTask(newTaskStatus, onboarding, lastSuccessfulTask))
                .defaultIfEmpty(buildTask(newTaskStatus, onboarding, null));
    }

    private SyncTask buildTask(TaskStatus taskStatus,
                               TenantOnboarding onboarding,
                               TaskStatusEntity lastSuccessfulTask) {
        return SyncTask.builder()
                .taskId(taskStatus.getTaskId())
                .integration(onboarding.getIntegration())
                .tenantId(onboarding.getTenantId())
                .scheduleTime(taskStatus.getStatusUpdateTime())
                .credentialsVaultPath(onboarding.getCredentialsVaultPath())
                .priorSuccessfulSync(buildTaskMetadata(lastSuccessfulTask))
                .tenantConfigurations(onboarding.getConfigurations())
                .taskType(taskStatus.getTaskType())
                .taskStatus(taskStatus.getStatusType())
                .statusMessage(taskStatus.getStatusMessage())
                .build();
    }

    private TaskBasicMetadata buildTaskMetadata(TaskStatusEntity lastSuccessfulTask) {
        if (lastSuccessfulTask == null) {
            return null;
        }
        return TaskBasicMetadata.builder()
                .startTime(DateTimeUtil.toString(lastSuccessfulTask.getStartTime()).orElse(null))
                .endTime(DateTimeUtil.toString(lastSuccessfulTask.getEndTime()).orElse(null))
                .endMessage(lastSuccessfulTask.getStatusMessage())
                .build();
    }

}