package com.illumio.data.components.consumer;

import com.illumio.data.components.mapping.IntegrationDataTranslatorOrchestrator;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.header.Header;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverRecord;

import java.nio.charset.StandardCharsets;

import static com.illumio.data.components.util.Constants.DATA_TYPE;

@Slf4j
@Service
@RequiredArgsConstructor
public class JsonInputConsumerService {

    private final KafkaReceiver<String, String> kafkaReceiver;
    private final IntegrationDataTranslatorOrchestrator integrationDataTranslatorOrchestrator;


    @EventListener(ApplicationReadyEvent.class)
    public void consumeJsonInput() {
        jsonInputConsumer()
                .subscribe();
    }

    @RetryReactiveOnError
    private Flux<Void> jsonInputConsumer() {
        return kafkaReceiver.receive()
                            .doOnNext(jsonInputRecord -> log.debug("Received json input message: key={}, value={}",
                                    jsonInputRecord.key(), jsonInputRecord.value()))
                            .flatMap(this::processRecord);
    }

    @LogReactiveExecutionTime
    private Flux<Void> processRecord(ReceiverRecord<String, String> record) {
        String dataType = extractDataType(record);
        String jsonData = record.value();

        return integrationDataTranslatorOrchestrator
            .mapJsonData(jsonData, dataType)
            .doFinally(signal -> CommonUtil.acknowledgeRecord(record, signal));
    }

    private String extractDataType(ReceiverRecord<String, String> record) {
        if (record.headers() != null) {
            Header dataTypeHeader = record.headers().lastHeader(DATA_TYPE);
            if (dataTypeHeader != null && dataTypeHeader.value() != null) {
                return new String(dataTypeHeader.value(), StandardCharsets.UTF_8);
            }
        }

        log.warn("No 'data-type' header found in message. Key: {}, Offset: {}",
                record.key(), record.offset());
        return null;
    }
}
