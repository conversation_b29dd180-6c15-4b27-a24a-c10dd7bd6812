    package com.illumio.data.components.mapping;

    import com.fasterxml.jackson.databind.JsonNode;
    import com.fasterxml.jackson.databind.ObjectMapper;
    import com.illumio.data.components.configuration.MappingConfigurationLoader;

    import com.illumio.data.components.model.MappingConfiguration;
    import com.illumio.data.components.producer.JsonOutputSenderService;
    import lombok.RequiredArgsConstructor;
    import lombok.extern.slf4j.Slf4j;
    import org.springframework.stereotype.Service;
    import reactor.core.publisher.Flux;
    import reactor.core.publisher.Mono;

    @Slf4j
    @RequiredArgsConstructor
    @Service
    public class IntegrationDataTranslatorOrchestrator {

        private final MappingConfigurationLoader configLoader;
        private final ObjectMapper objectMapper;
        private final ArrayIndexFinder arrayIndexFinder;
        private final JsonObjectMapper jsonObjectMapper;
        private final JsonOutputSenderService jsonOutputSenderService;

        public Flux<Void> mapJsonData(String jsonData, String dataType) {
            return processMapping(jsonData, dataType)
                    .flatMap(mappedData -> jsonOutputSenderService.sendMappedData(mappedData, dataType))
                    .onErrorResume(error -> {
                        log.error("Error processing JSON mapping for data type: {}", dataType, error);
                            return Mono.empty();
                    });
        }

        private Flux<String> processMapping(String jsonData, String dataType) {
            return configLoader.loadMappingConfig(dataType)
                               .flatMapMany(config -> parseJson(jsonData)
                                       .flatMapMany(rootNode -> mapJsonToObjects(rootNode, config))
                               );
        }

        private Mono<JsonNode> parseJson(String jsonData) {
            return Mono.fromCallable(() -> objectMapper.readTree(jsonData))
                       .onErrorResume(e -> {
                           log.error("Failed to parse JSON", e);
                           return Mono.error(e);
                       });
        }

        private Flux<String> mapJsonToObjects(JsonNode rootNode, MappingConfiguration config) {
            return arrayIndexFinder.findArrayIndices(rootNode, config)
                                   .flatMapMany(indices -> {
                                       if (indices.isEmpty()) {
                                           return jsonObjectMapper.mapSingleObject(rootNode, config, -1);
                                       } else {
                                           return Flux.fromIterable(indices)
                                                      .flatMap(index -> jsonObjectMapper.mapSingleObject(rootNode, config, index));
                                       }
                                   });
        }
    }
