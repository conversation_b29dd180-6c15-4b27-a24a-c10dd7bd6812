package com.illumio.data.components.mapping.strategy.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.mapping.JsonNodeNavigator;
import com.illumio.data.components.mapping.JsonPathResolver;
import com.illumio.data.components.mapping.JsonValueConverter;
import com.illumio.data.components.mapping.TestResourceLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SingleValueExtractionStrategyTest {

    @Mock
    private JsonPathResolver pathResolver;

    @Mock
    private JsonNodeNavigator navigator;

    @Mock
    private JsonValueConverter converter;

    @InjectMocks
    private SingleValueExtractionStrategy strategy;

    private JsonNode testJsonNode;

    @BeforeEach
    void setUp() throws IOException {
        testJsonNode = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");
    }

    @Test
    void supports_shouldReturnTrue_whenPathHasNoMarkers() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void supports_shouldReturnFalse_whenPathHasCollectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collection].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void supports_shouldReturnFalse_whenPathHasObjectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@object].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void supports_shouldReturnFalse_whenPathHasBothMarkers() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collection][@object].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void supports_shouldReturnTrue_whenPathIsSimple() {
        // Given
        String jsonPath = "data.name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void extract_shouldReturnValue_whenPathExists() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].name";
        int arrayIndex = 0;
        JsonNode targetNode = TestResourceLoader.createJsonNode("\"CVE-2022-1304\"");
        Object expectedValue = "CVE-2022-1304";

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(targetNode));
        when(converter.convertJsonNodeToValue(targetNode)).thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmpty_whenPathDoesNotExist() {
        // Given
        String jsonPath = "data.nonExistent.field";
        String resolvedPath = "data.nonExistent.field";
        int arrayIndex = 0;

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.empty());

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmpty_whenConverterReturnsEmpty() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].name";
        int arrayIndex = 0;
        JsonNode targetNode = TestResourceLoader.createNullJsonNode();

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(targetNode));
        when(converter.convertJsonNodeToValue(targetNode)).thenReturn(Mono.empty());

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extract_shouldHandleNumericValues() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].score";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].score";
        int arrayIndex = 0;
        JsonNode targetNode = TestResourceLoader.createJsonNode("7.8");
        Object expectedValue = 7.8;

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(targetNode));
        when(converter.convertJsonNodeToValue(targetNode)).thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                    assertThat(value).isInstanceOf(Double.class);
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldHandleBooleanValues() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].hasExploit";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].hasExploit";
        int arrayIndex = 0;
        JsonNode targetNode = TestResourceLoader.createJsonNode("false");
        Object expectedValue = false;

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(targetNode));
        when(converter.convertJsonNodeToValue(targetNode)).thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                    assertThat(value).isInstanceOf(Boolean.class);
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldHandleNegativeArrayIndex() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        String resolvedPath = "data.vulnerabilityFindings.nodes.name";
        int arrayIndex = -1;

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.empty());

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extract_shouldHandleNestedObjectPath() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].vulnerableAsset.name";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].vulnerableAsset.name";
        int arrayIndex = 0;
        JsonNode targetNode = TestResourceLoader.createJsonNode("\"saki-RH8-ven-lab02\"");
        Object expectedValue = "saki-RH8-ven-lab02";

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(targetNode));
        when(converter.convertJsonNodeToValue(targetNode)).thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldHandleZeroArrayIndex() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].name";
        int arrayIndex = 0;
        JsonNode targetNode = TestResourceLoader.createJsonNode("\"CVE-2022-1304\"");
        Object expectedValue = "CVE-2022-1304";

        when(pathResolver.resolveArrayPath(jsonPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(targetNode));
        when(converter.convertJsonNodeToValue(targetNode)).thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }
}
