# Integration Data Translator

A Spring Boot reactive service that transforms JSON data from security integrations using configurable mapping rules.

## Overview

Transforms raw JSON responses from security platforms (Wiz, Armis, etc.) into standardized formats using:
- **Configuration-driven mapping** - JSON-based rules
- **Complex data structures** - Arrays, nested objects, collections
- **Reactive processing** - Spring WebFlux for high performance

## Architecture

For detailed architecture documentation, refer to: https://confluence.illum.io/pages/viewpage.action?pageId=356044717

```
IntegrationDataTranslatorOrchestrator
├── MappingConfigurationLoader    # Loads JSON mapping configurations
├── JsonObjectMapper             # Maps JSON objects using configurations
├── ArrayIndexFinder            # Finds array indices for wildcard paths
├── ValueExtractor              # Extracts values using strategy pattern
│   ├── SingleValueExtractionStrategy    # Extracts single values
│   ├── ObjectExtractionStrategy         # Extracts complete objects [@object]
│   └── CollectionExtractionStrategy     # Extracts collections [@collection]
├── JsonNodeNavigator           # Navigates JSON structures
├── JsonPathResolver            # Resolves JSON paths and wildcards
└── JsonValueConverter          # Converts JsonNode to Java objects
```

## Configuration Format

```json
{
  "name": "VulnerabilityDataMapping",
  "description": "Mapping configuration for vulnerability meta data",
  "fieldMappings": {
    "cveId": "data.vulnerabilityFindings.nodes[*].name",
    "severity": "data.vulnerabilityFindings.nodes[*].severity",
    "tags": "data.vulnerabilityFindings.nodes[*].tags[@collection]",
    "assetDetails": "data.vulnerabilityFindings.nodes[*].vulnerableAsset[@object]"
  }
}
```

### Path Patterns

| Pattern | Description | Example |
|---------|-------------|---------|
| `field.subfield` | Dot notation | `data.name` |
| `array[*].field` | Array wildcard | `data.items[*].id` |
| `field[@collection]` | Collection extraction | `data.tags[@collection]` |
| `field[@object]` | Object extraction | `data.metadata[@object]` |

## Local Development

### Starting the Server
Refer to the [local-infra README](../../local-infra/README.md) for complete local setup instructions.

**Important Notes:**
- A `local-dev` folder is created where mapping rules are picked up from
- When running directly from IntelliJ, check the `mapping.rules-path: ${user.dir}/local-dev/mapping-rules` in `application-local-docker.yml` is correct, since the `${user.dir}` changes 