{{- range $deploymentGroup := .Values.deploymentGroups }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "IntegrationDataTranslator.fullname" $ }}-{{ $deploymentGroup.name }}
  labels:
    {{- include "IntegrationDataTranslator.labels" $ | nindent 4 }}
    group: {{ $deploymentGroup.name }}
spec:
  {{- if not $.Values.autoscaling.enabled }}
  replicas: {{ $deploymentGroup.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "IntegrationDataTranslator.selectorLabels" $ | nindent 6 }}
      group: {{ $deploymentGroup.name }}
      {{- if $.Values.extraLabels }}
        {{ toYaml $.Values.extraLabels | nindent 6 }}
      {{- end }}
  template:
    metadata:
      annotations:
      {{- with $.Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.podMetricsAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "IntegrationDataTranslator.selectorLabels" $ | nindent 8 }}
        group: {{ $deploymentGroup.name }}
        {{- if $.Values.extraLabels }}
          {{ toYaml $.Values.extraLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- with $.Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "IntegrationDataTranslator.serviceAccountName" $ }}
      securityContext:
        {{- toYaml $.Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ $.Chart.Name }}
          securityContext:
            {{- toYaml $.Values.securityContext | nindent 12 }}
          image: "{{ $.Values.image.repositoryBase }}/{{ $.Values.image.repositoryName }}:{{ $.Values.image.tag | default $.Chart.AppVersion }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
          env:
            - name: INTEGRATION_DATA_TRANSLATOR_MAPPING_RULES_PATH
              value: "/var/resources/mapping-rules"
          envFrom:
            - secretRef:
                name: {{ include "IntegrationDataTranslator.fullname" $ }}-env-secrets
          ports:
          {{- range $.Values.ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: TCP
          {{- end }}
          resources:
            {{- toYaml $.Values.resources | nindent 12 }}
          volumeMounts:
            # Mount for application configuration
            - name: {{ include "IntegrationDataTranslator.fullname" $ }}-env-configmap
              mountPath: /var/resources/
            # Mount for mapping rules JSON files
            - name: {{ include "IntegrationDataTranslator.fullname" $ }}-env-mapping-rules
              mountPath: /var/resources/mapping-rules
              readOnly: true
      volumes:
        # Volume for application configuration
        - name: {{ include "IntegrationDataTranslator.fullname" $ }}-env-configmap
          configMap:
            name: {{ include "IntegrationDataTranslator.fullname" $ }}-env-configmap
        # Volume for mapping rules
        - name: {{ include "IntegrationDataTranslator.fullname" $ }}-env-mapping-rules
          configMap:
            name: {{ include "IntegrationDataTranslator.fullname" $ }}-env-mapping-rules

      {{- with $.Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
{{- end }}