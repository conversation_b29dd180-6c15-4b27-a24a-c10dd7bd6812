# Default values for connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
server:
  port: 8080
  ssl:
    certificate: ""
    certificatePrivateKey: ""

ports:
- name: admin
  port: 8084
- name: rest
  port: 8080

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8080

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/integrations
  repositoryName: integrations-manager
  tag:      # value given at helm deployment
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

ansi:
  enabled: ALWAYS

spring:
  cloud:
    azure:
      cosmos:
        endpoint: "https://integrationstest.documents.azure.com:443/"
        key:    # should give at deployment time
        database: "integrations"

  data:
    r2dbc:
      repositories:
        enabled: true
  r2dbc:
    url: r2dbc:postgresql://c-integrationstest.mmtdd3hnha22tf.postgres.cosmos.azure.com:5432/integrations
    username: citus
    password:    # should give at deployment time
    properties:
      sslMode: require # refer to https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-PROTECTION for possible values
  datasource:
    driverClassName: org.postgresql.Driver

vault:
  uri: "http://127.0.0.1:8200"
  token:    # should give at deployment time
  namespace: admin

census:
  auth:
    enabled: true
grpc:
  channel:
    host: dev-census-grpc.console.ilabs.io
    port: 443
    enableMtls: false
    caCert: #should be given during deployment time
    mtlsKey:  #should be given during deployment time
    mtlsCert:  #should be given during deployment time
  server:
    port: 9091

launchDarkly:
  sdkKey:    # should give at deployment time

retryConfig:
  userOperations:
    minBackoff: 2s
    maxRetries: 2
  systemOperations:
    minBackoff: 30s
    maxRetries: 2

integrationsManager:
  integrationsConfig:
    wiz:
      name: Wiz
      description: Ingest cloud security issues and vulnerabilities from Wiz
      apiHost: wiz.io
    armis:
      name: Armis
      description: Import Armis IoT/OT inventory and asset info to Illumio
      apiHost: armis.com
    checkpoint:
      name: Checkpoint
      description: Ingest Checkpoint firewall logs for Insights
  kafkaLightTaskProducerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "light-task-queue"
    isConnectionString: true
    connectionString: # should give at deployment time
  kafkaHeavyTaskProducerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "heavy-task-queue"
    isConnectionString: true
    connectionString: # should give at deployment time

extraLabels: {}

ingress:
  enabled: true
  pathPrefixes:
    - /api/v1/tenants
  ingressClassName: nginx
  certManager:
    enabled: true
    clusterIssuer: "cert-manager-letsencrypt-prod-route53"
  fqdn: dev.integrations.imc.ilabs.io
  tlsSecretName: dev-integrations-manager-tls
  annotations: {}
