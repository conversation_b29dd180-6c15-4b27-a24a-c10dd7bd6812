apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "IntegrationsManager.fullname" . }}-env-configmap
  labels:
    {{- include "IntegrationsManager.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    spring:
      application:
        name: "integrations-manager"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
      cloud:
        azure:
          cosmos:
            endpoint: "{{.Values.spring.cloud.azure.cosmos.endpoint}}"
            database: "{{.Values.spring.cloud.azure.cosmos.database}}"
      data:
        r2dbc:
          repositories:
            enabled: {{.Values.spring.data.r2dbc.repositories.enabled}}
      r2dbc:
        url: "{{.Values.spring.r2dbc.url}}"
        username: "{{.Values.spring.r2dbc.username}}"
        properties:
          sslMode: "{{.Values.spring.r2dbc.properties.sslMode}}"
      datasource:
        driver-class-name: "{{.Values.spring.datasource.driverClassName}}"
    server:
      port: {{.Values.server.port}}
      ssl:
        enabled: true
        certificate: {{.Values.server.ssl.certificate}}
        certificate-private-key: {{.Values.server.ssl.certificatePrivateKey}}
    vault:
      uri: "{{.Values.vault.uri}}"
      namespace: "{{.Values.vault.namespace}}"
    census:
      auth:
        enabled: {{.Values.census.auth.enabled}}
    grpc:
      channel:
        host: "{{.Values.grpc.channel.host}}"
        port: {{.Values.grpc.channel.port}}
        enableMtls: {{.Values.grpc.channel.enableMtls}}
        mtlsCert: "{{.Values.grpc.channel.mtlsCert}}"
        mtlsKey: "{{.Values.grpc.channel.mtlsKey}}"
        caCert: "{{.Values.grpc.channel.caCert}}"
      server:
        port: {{.Values.grpc.server.port}}
    retry-config:
      user-operations:
        min-backoff: "{{.Values.retryConfig.userOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.userOperations.maxRetries}}
      system-operations:
        min-backoff: "{{.Values.retryConfig.systemOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.systemOperations.maxRetries}}

    integrations-manager:
      integrations-config:
        wiz:
          name: "{{.Values.integrationsManager.integrationsConfig.wiz.name}}"
          description: "{{.Values.integrationsManager.integrationsConfig.wiz.description}}"
          apiHost: "{{.Values.integrationsManager.integrationsConfig.wiz.apiHost}}"
        armis:
          name: "{{.Values.integrationsManager.integrationsConfig.armis.name}}"
          description: "{{.Values.integrationsManager.integrationsConfig.armis.description}}"
          apiHost: "{{.Values.integrationsManager.integrationsConfig.armis.apiHost}}"
        checkpoint:
          name: "{{.Values.integrationsManager.integrationsConfig.checkpoint.name}}"
          description: "{{.Values.integrationsManager.integrationsConfig.checkpoint.description}}"
          apiHost: "{{.Values.integrationsManager.integrationsConfig.checkpoint.apiHost}}"
      kafka-light-task-producer-config:
        bootstrapServers: "{{.Values.integrationsManager.kafkaLightTaskProducerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationsManager.kafkaLightTaskProducerConfig.topic}}"
        isConnectionString: {{.Values.integrationsManager.kafkaLightTaskProducerConfig.isConnectionString}}
      kafka-heavy-task-producer-config:
        bootstrapServers: "{{.Values.integrationsManager.kafkaHeavyTaskProducerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationsManager.kafkaHeavyTaskProducerConfig.topic}}"
        isConnectionString: {{.Values.integrationsManager.kafkaHeavyTaskProducerConfig.isConnectionString}}