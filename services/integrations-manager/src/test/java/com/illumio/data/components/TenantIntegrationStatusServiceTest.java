package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.IntegrationsManagerConfig;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.WizTenantConfigurations;
import com.illumio.data.model.mapper.TenantOnboardingMapper;
import com.illumio.data.util.DateTimeUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.OffsetDateTime;

import java.util.Collections;
import java.util.Objects;

import static com.illumio.data.util.IntegrationManagerConstants.UI_TENANT_STATUS_NOT_ONBOARDED_MESSAGE;
import static com.illumio.data.util.IntegrationManagerConstants.UI_TENANT_STATUS_ONBOARDING_MESSAGE;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TenantIntegrationStatusServiceTest {

    @Mock private TaskDbService taskDbService;
    @Mock private OnboardingDbService onboardingDbService;
    @Mock private CredentialsService credentialsService;
    @Mock private LDService ldService;
    @Mock private IntegrationsManagerConfig integrationsManagerConfig;

    @InjectMocks private TenantIntegrationStatusService tenantIntegrationStatusService;

    private TenantIntegrationStatusRequest request;
    private TenantOnboardingEntity tenantOnboardingEntity;
    private TenantOnboardingEntity tenantOnboardingEntity2;
    private TenantOnboarding tenantOnboarding;
    private TenantOnboarding tenantOnboarding2;
    private TaskStatusEntity taskStatusEntity;
    private Credentials credentials;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String WIZ_AUTH_URL = "https://auth.app.wiz.io/oauth/token";
    private static final String WIZ_API_URL = "https://api.us41.app.wiz.io/graphql";

    @BeforeEach
    void setUp() {
        // Initialize test data
        request = new TenantIntegrationStatusRequest();
        request.setTenantId("tenant-123");
        request.setIntegration(Integration.WIZ);

        tenantOnboardingEntity = new TenantOnboardingEntity(
                request.getTenantId(),
                request.getIntegration(),
                "secret/clients/client-789/credentials",
                new WizTenantConfigurations(WIZ_API_URL, WIZ_AUTH_URL),
                Collections.singletonMap("cloudSecureTenantId", "csTenantId"));
        tenantOnboardingEntity2 = new TenantOnboardingEntity(
                tenantOnboardingEntity.getTenantId(),
                Integration.ARMIS,
                tenantOnboardingEntity.getCredentialsVaultPath(),
                tenantOnboardingEntity.getConfigurations(),
                Collections.emptyMap()
        );
        tenantOnboarding = TenantOnboardingMapper.toTenantOnboarding(tenantOnboardingEntity, objectMapper);
        tenantOnboarding2 = TenantOnboardingMapper.toTenantOnboarding(tenantOnboardingEntity2, objectMapper);


        taskStatusEntity = new TaskStatusEntity();
        taskStatusEntity.setStatus(TaskStatusType.SUCCESS);
        taskStatusEntity.setStatusMessage("Sync completed successfully");
        taskStatusEntity.setEndTime(OffsetDateTime.now());

        credentials = new Credentials();
        credentials.setClientId("client-789");
    }

    @Test
    void fetchTenantIntegrationStatus_integrationEnabled_Success() {
        // Arrange
        when(onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(tenantOnboarding));
        when(credentialsService.getCredentials(tenantOnboardingEntity.getCredentialsVaultPath()))
                .thenReturn(Mono.just(credentials));
        when(taskDbService.fetchLastCompletedTask(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(taskStatusEntity));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));

        // Act & Assert
        StepVerifier.create(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                    .expectNextMatches(response -> {

                        return response.getIsOnboarded() &&
                                response.getStatus().equals(UiTenantStatus.valueOf(taskStatusEntity.getStatus().name())) &&
                                response.getStatusMessage().equals(taskStatusEntity.getStatusMessage()) &&
                                response.getLastUpdateTime().equals(DateTimeUtil.toString(taskStatusEntity.getEndTime()).orElse(null)) &&
                                response.getAuthURL().equals(WIZ_AUTH_URL) &&
                                response.getApiURL().equals(WIZ_API_URL) &&
                                response.getClientId().equals(credentials.getClientId()) &&
                                response.getIsEnabled();
                    })
                    .verifyComplete();

        verify(onboardingDbService).fetchTenantOnboarding(request.getTenantId(), request.getIntegration());
        verify(credentialsService).getCredentials(tenantOnboardingEntity.getCredentialsVaultPath());
        verify(taskDbService).fetchLastCompletedTask(request.getTenantId(), request.getIntegration());
        verify(ldService).integrationIsEnabledForTenant(request.getTenantId(), request.getIntegration());
    }


    @Test
    void fetchTenantIntegrationStatus_integrationDisabled_Success() {
        // Arrange
        when(onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(tenantOnboarding));
        when(credentialsService.getCredentials(tenantOnboardingEntity.getCredentialsVaultPath()))
                .thenReturn(Mono.just(credentials));
        when(taskDbService.fetchLastCompletedTask(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(taskStatusEntity));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(false));

        // Act & Assert
        StepVerifier.create(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                    .expectNextMatches(response -> {
                        return response.getIsOnboarded() &&
                                response.getStatus().equals(UiTenantStatus.valueOf(taskStatusEntity.getStatus().name())) &&
                                response.getStatusMessage().equals(taskStatusEntity.getStatusMessage()) &&
                                response.getLastUpdateTime().equals(DateTimeUtil.toString(taskStatusEntity.getEndTime()).orElse(null)) &&
                                response.getAuthURL().equals(WIZ_AUTH_URL) &&
                                response.getApiURL().equals(WIZ_API_URL) &&
                                response.getClientId().equals(credentials.getClientId()) &&
                                response.getIsEnabled().equals(false);
                    })
                    .verifyComplete();

        verify(onboardingDbService).fetchTenantOnboarding(request.getTenantId(), request.getIntegration());
        verify(credentialsService).getCredentials(tenantOnboardingEntity.getCredentialsVaultPath());
        verify(taskDbService).fetchLastCompletedTask(request.getTenantId(), request.getIntegration());
        verify(ldService).integrationIsEnabledForTenant(request.getTenantId(), request.getIntegration());
    }

    @Test
    void fetchTenantIntegrationStatus_OnboardedButNoCompletedTask() {
        // Arrange
        when(onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(tenantOnboarding));
        when(credentialsService.getCredentials(tenantOnboardingEntity.getCredentialsVaultPath()))
                .thenReturn(Mono.just(credentials));
        when(taskDbService.fetchLastCompletedTask(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.empty());
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));

        // Act & Assert
        StepVerifier.create(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                    .expectNextMatches(response ->
                            response.getIsOnboarded() &&
                                    response.getStatus().equals(UiTenantStatus.ONBOARDING) &&
                                    UI_TENANT_STATUS_ONBOARDING_MESSAGE.equals(response.getStatusMessage()) &&
                                    response.getAuthURL().equals(WIZ_AUTH_URL) &&
                                    response.getApiURL().equals(WIZ_API_URL) &&
                                    response.getClientId().equals(credentials.getClientId()) &&
                                    response.getIsEnabled().equals(true))
                    .verifyComplete();

        verify(onboardingDbService).fetchTenantOnboarding(request.getTenantId(), request.getIntegration());
        verify(credentialsService).getCredentials(tenantOnboardingEntity.getCredentialsVaultPath());
        verify(taskDbService).fetchLastCompletedTask(request.getTenantId(), request.getIntegration());
    }

    @Test
    void fetchTenantIntegrationStatus_TenantOnboardingNotFound() {
        // Arrange
        when(onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.empty());
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));

        // Act & Assert
        StepVerifier.create(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                    .expectNextMatches(response ->
                            !response.getIsOnboarded() &&
                                    UI_TENANT_STATUS_NOT_ONBOARDED_MESSAGE.equals(response.getStatusMessage()) &&
                                    response.getStatus() == null &&
                                    response.getClientId() == null &&
                                    response.getLastUpdateTime() == null &&
                                    response.getIsEnabled().equals(true))
                    .verifyComplete();

        verify(onboardingDbService).fetchTenantOnboarding(request.getTenantId(), request.getIntegration());
        verify(credentialsService, never()).getCredentials(anyString());
        verify(taskDbService, never()).fetchLastCompletedTask(anyString(), any());
    }

    @Test
    void fetchTenantIntegrationStatus_CredentialsServiceError() {
        // Arrange
        when(onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(tenantOnboarding));
        when(credentialsService.getCredentials(tenantOnboardingEntity.getCredentialsVaultPath()))
                .thenReturn(Mono.error(new RuntimeException("Credentials service error")));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));

        // Act & Assert
        StepVerifier.create(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                    .expectErrorMatches(error ->
                            error instanceof ResponseStatusException &&
                                    ((ResponseStatusException) error).getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR &&
                                    Objects.requireNonNull(((ResponseStatusException) error).getReason()).contains("Error fetching onboarding sync status"))
                    .verify();

        verify(onboardingDbService).fetchTenantOnboarding(request.getTenantId(), request.getIntegration());
        verify(credentialsService).getCredentials(tenantOnboardingEntity.getCredentialsVaultPath());
        verify(taskDbService, never()).fetchLastCompletedTask(anyString(), any());
    }

    @Test
    void fetchTenantIntegrationStatus_TaskDbServiceError() {
        // Arrange
        when(onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(tenantOnboarding));
        when(credentialsService.getCredentials(tenantOnboardingEntity.getCredentialsVaultPath()))
                .thenReturn(Mono.just(credentials));
        when(taskDbService.fetchLastCompletedTask(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.error(new RuntimeException("Task DB service error")));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));

        // Act & Assert
        StepVerifier.create(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                    .expectErrorMatches(error ->
                            error instanceof ResponseStatusException &&
                                    ((ResponseStatusException) error).getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR &&
                                    Objects.requireNonNull(((ResponseStatusException) error).getReason()).contains("Error fetching onboarding sync status"))
                    .verify();

        verify(onboardingDbService).fetchTenantOnboarding(request.getTenantId(), request.getIntegration());
        verify(credentialsService).getCredentials(tenantOnboardingEntity.getCredentialsVaultPath());
        verify(taskDbService).fetchLastCompletedTask(request.getTenantId(), request.getIntegration());
    }

    @Test
    void fetchTenantIntegrationStatus_LDServiceError() {
        // Arrange
        when(onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.just(tenantOnboarding));
        when(credentialsService.getCredentials(tenantOnboardingEntity.getCredentialsVaultPath()))
                .thenReturn(Mono.just(credentials));
        when(taskDbService.fetchLastCompletedTask(request.getTenantId(), request.getIntegration()))
                .thenReturn(Mono.empty());
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.error(new RuntimeException("LD error")));

        // Act & Assert
        StepVerifier.create(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                    .expectErrorMatches(error ->
                            error instanceof ResponseStatusException &&
                                    ((ResponseStatusException) error).getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR &&
                                    Objects.requireNonNull(((ResponseStatusException) error).getReason()).contains("Error fetching onboarding sync status"))
                    .verify();

        verify(onboardingDbService).fetchTenantOnboarding(request.getTenantId(), request.getIntegration());
        verify(credentialsService).getCredentials(tenantOnboardingEntity.getCredentialsVaultPath());
        verify(taskDbService).fetchLastCompletedTask(request.getTenantId(), request.getIntegration());
    }

    @Test
    void getEligibleIntegrations_Success() {
        when(onboardingDbService.fetchTenantOnboardings(tenantOnboardingEntity.getTenantId()))
                .thenReturn(Flux.just(tenantOnboarding, tenantOnboarding2));
        when(ldService.integrationIsEnabledForTenant(tenantOnboardingEntity.getTenantId(), tenantOnboardingEntity.getIntegration()))
                .thenReturn(Mono.just(true));
        when(ldService.integrationIsEnabledForTenant(tenantOnboardingEntity.getTenantId(), tenantOnboardingEntity2.getIntegration()))
                .thenReturn(Mono.just(false));
        when(ldService.integrationIsEnabledForTenant(tenantOnboardingEntity.getTenantId(), Integration.CHECKPOINT))
                .thenReturn(Mono.just(false));

        final IntegrationResponseItem expectedResponseItem1 = IntegrationResponseItem.builder()
                                                                             .id(tenantOnboardingEntity.getIntegration())
                                                                             .isOnboarded(true)
                                                                             .build();

        StepVerifier.create(tenantIntegrationStatusService.getEligibleIntegrations(tenantOnboardingEntity.getTenantId()))
                .expectNext(expectedResponseItem1)
                .verifyComplete();
    }

    @Test
    void getEligibleIntegrations_PropagatesOnboardingDbError() {
        when(onboardingDbService.fetchTenantOnboardings(tenantOnboardingEntity.getTenantId()))
                .thenReturn(Flux.error(new RuntimeException("Onboarding DB error")));

        StepVerifier.create(tenantIntegrationStatusService.getEligibleIntegrations(tenantOnboardingEntity.getTenantId()))
                    .expectError(RuntimeException.class)
                    .verify();
    }

    @Test
    void getEligibleIntegrations_PropagatesLDError() {
        when(onboardingDbService.fetchTenantOnboardings(tenantOnboardingEntity.getTenantId()))
                .thenReturn(Flux.just(tenantOnboarding, tenantOnboarding2));
        when(ldService.integrationIsEnabledForTenant(tenantOnboardingEntity.getTenantId(), tenantOnboardingEntity.getIntegration()))
                .thenReturn(Mono.error(new RuntimeException("Onboarding DB error")));
        when(ldService.integrationIsEnabledForTenant(tenantOnboardingEntity.getTenantId(), tenantOnboardingEntity2.getIntegration()))
                .thenReturn(Mono.just(false));

        StepVerifier.create(tenantIntegrationStatusService.getEligibleIntegrations(tenantOnboardingEntity.getTenantId()))
                    .expectError(RuntimeException.class)
                    .verify();
    }

}
