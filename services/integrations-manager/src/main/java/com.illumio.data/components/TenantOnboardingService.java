package com.illumio.data.components;

import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.IntegrationCredentials;
import com.illumio.data.model.*;
import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
@RequiredArgsConstructor
public class TenantOnboardingService {

    private final TenantValidationService tenantValidationService;
    private final CredentialsService credentialsService;
    private final TaskSenderService taskSenderService;
    private final OnboardingDbService onboardingDbService;

    @LogReactiveExecutionTime
    public Mono<Void> onboardTenant(final TenantOnboardingRequest tenantOnboardingRequest, boolean isGreenfieldOnboarding) {
        return tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, isGreenfieldOnboarding)
                .flatMap(shouldOnboard -> processOnboarding(tenantOnboardingRequest, isGreenfieldOnboarding));
    }

    private Mono<Void> processOnboarding(final TenantOnboardingRequest tenantOnboardingRequest, boolean isGreenfieldOnboarding) {
        final AtomicReference<String> credentialsVaultPathRef = new AtomicReference<>();
    return credentialsService
        .storeSensitiveDetails(
            IntegrationCredentials.builder()
                .tenantId(tenantOnboardingRequest.getTenantId())
                .integration(tenantOnboardingRequest.getIntegration())
                .credentials(tenantOnboardingRequest.getCredentials())
                .build())
        .flatMap(
            credentialsVaultPath -> {
              credentialsVaultPathRef.set(credentialsVaultPath);
              if (tenantOnboardingRequest.getIntegration().equals(Integration.WIZ)
                  && isGreenfieldOnboarding) {
                return scheduleOnboardingSyncTask(tenantOnboardingRequest, credentialsVaultPath)
                    .thenReturn(credentialsVaultPath);
              } else {
                return Mono.just(credentialsVaultPath);
              }
            })
        .flatMap(
            credentialsVaultPath ->
                onboardingDbService.saveTenantOnboarding(
                    tenantOnboardingRequest, credentialsVaultPath))
        .doOnError(
            error ->
                handleOnboardingError(
                    tenantOnboardingRequest, credentialsVaultPathRef.get(), error));
    }

    private void handleOnboardingError(final TenantOnboardingRequest tenantOnboardingRequest,
                                             final String credentialsVaultPath,
                                             final Throwable error) {
        log.error("Tenant onboarding for tenantId={} to integration={} failed.",
                tenantOnboardingRequest.getTenantId(), tenantOnboardingRequest.getIntegration(), error);
        if (credentialsVaultPath != null) {
            credentialsService.deleteSensitiveDetails(credentialsVaultPath)
                              .doOnSuccess(none ->
                                      log.debug("Successfully deleted Vault credentials due to failed onboarding for tenantId={} and integration={}.",
                                          tenantOnboardingRequest.getTenantId(), tenantOnboardingRequest.getIntegration()))
                              .subscribe();
        }
    }

    private Mono<Void> scheduleOnboardingSyncTask(final TenantOnboardingRequest onboardingRequest, final String credentialsVaultPath) {
        final String scheduleTime = DateTimeUtil.generateCurrentTimestamp();
        final String taskId = Util.generateNewTaskId(
                onboardingRequest.getTenantId(),
                onboardingRequest.getIntegration(),
                scheduleTime);

        return taskSenderService.sendTask(
                SyncTask.builder()
                        .taskId(taskId)
                        .tenantId(onboardingRequest.getTenantId())
                        .integration(onboardingRequest.getIntegration())
                        .scheduleTime(scheduleTime)
                        .taskStatus(TaskStatusType.getInitialTaskStatus(onboardingRequest.getIntegration()))
                        .credentialsVaultPath(credentialsVaultPath)
                        .tenantConfigurations(onboardingRequest.getConfigurations())
                        .taskType(TaskType.GREENFIELD)
                        .build());
    }

}