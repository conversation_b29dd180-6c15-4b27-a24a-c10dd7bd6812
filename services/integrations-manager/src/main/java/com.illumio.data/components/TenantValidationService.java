package com.illumio.data.components;

import com.illumio.data.configuration.IntegrationsManagerConfig;
import com.illumio.data.exception.BadRequestException;
import com.illumio.data.exception.ExternalServiceException;
import com.illumio.data.model.TokenRequest;
import com.illumio.data.model.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import static com.illumio.data.util.IntegrationManagerConstants.WIZ_HOST_DEFAULT;
import static com.illumio.data.util.IntegrationManagerConstants.ARMIS_HOST_DEFAULT;

@Slf4j
@Component
@RequiredArgsConstructor
public class TenantValidationService {

    private final LDService ldService;
    private final OnboardingDbService onboardingDbService;
    private final TokenGenerationService tokenGenerationService;
    private final IntegrationsManagerConfig integrationsManagerConfig;

    public Mono<Boolean> validateTenantOnboardingRequest(final TenantOnboardingRequest tenantOnboardingRequest, final boolean isGreenfieldOnboarding) {
        log.debug("Validating onboarding eligibility for tenantId={}  integration={} isGreenfieldOnboarding={}",
                tenantOnboardingRequest.getTenantId(), tenantOnboardingRequest.getIntegration(), isGreenfieldOnboarding);

    return checkOnboardingCondition(
            configurationUrlIsValid(
                tenantOnboardingRequest.getConfigurations().getAuthUrl(),
                tenantOnboardingRequest.getIntegration()),
            new BadRequestException("Invalid authentication URL. URL must be properly formatted"))
        .flatMap(
            v ->
                checkOnboardingCondition(
                    configurationUrlIsValid(
                        tenantOnboardingRequest.getConfigurations().getApiUrl(),
                        tenantOnboardingRequest.getIntegration()),
                    new BadRequestException("Invalid API URL. URL must be properly formatted")))
        .flatMap(
            v ->
                checkOnboardingCondition(
                    ldService.integrationIsEnabledForTenant(
                        tenantOnboardingRequest.getTenantId(),
                        tenantOnboardingRequest.getIntegration()),
                    new BadRequestException("Integration is not enabled for tenant")))
        .flatMap(
            v ->
                onboardingDbService
                    .isTenantOnboarded(tenantOnboardingRequest)
                    .flatMap(
                        isOnboarded -> {
                          if (isGreenfieldOnboarding
                              && Boolean.TRUE.equals(isOnboarded)) {
                            return Mono.error(
                                new BadRequestException(
                                    "Tenant is already onboarded to integration"));
                          } else if (!isGreenfieldOnboarding
                              && Boolean.FALSE.equals(isOnboarded)) {
                            return Mono.error(new BadRequestException("Tenant and integration not onboarded"));
                          } else {
                            return Mono.just(Boolean.TRUE);
                          }
                        }))
        .flatMap(
            v ->
                tokenGenerationService
                    .getToken(generateTokenRequest(tenantOnboardingRequest))
                    .onErrorResume(ex -> Mono.error(new ExternalServiceException(ex.getMessage()))))
        .map(v -> true);
    }

    private Mono<Boolean> checkOnboardingCondition(final Mono<Boolean> condition, final Throwable error) {
        return condition
                .doOnError(Mono::error)
                .filter(Boolean.TRUE::equals)
                .switchIfEmpty(Mono.error(error));
    }

    private Mono<Boolean> configurationUrlIsValid(final String url, final Integration integration) {
        try {
            URI uri = new URI(url);
            String expectedConfigUrlHost = switch (integration) {
                case WIZ ->
                        Optional.ofNullable(integrationsManagerConfig.getIntegrationsConfig())
                                .map(IntegrationsManagerConfig.IntegrationsConfig::getWiz)
                                .map(IntegrationsManagerConfig.IntegrationConfig::getApiHost)
                                .orElse(WIZ_HOST_DEFAULT);
                case ARMIS ->
                        Optional.ofNullable(integrationsManagerConfig.getIntegrationsConfig())
                                                        .map(IntegrationsManagerConfig.IntegrationsConfig::getArmis)
                                                        .map(IntegrationsManagerConfig.IntegrationConfig::getApiHost)
                                                        .orElse(ARMIS_HOST_DEFAULT);
                case CHECKPOINT -> throw new UnsupportedOperationException("Checkpoint is not yet fully integrated in the framework");
            };
            return Mono.just(uri.getHost() != null && uri.getHost().endsWith(expectedConfigUrlHost));
        } catch (URISyntaxException e) {
            return Mono.just(false);
        }
    }

    private TokenRequest generateTokenRequest(final TenantOnboardingRequest onboardingRequest) {
        return TokenRequest.builder()
                           .tenantId(onboardingRequest.getTenantId())
                           .integration(onboardingRequest.getIntegration())
                           .credentials(onboardingRequest.getCredentials())
                           .authUrl(onboardingRequest.getConfigurations().getAuthUrl())
                           .build();
    }

}