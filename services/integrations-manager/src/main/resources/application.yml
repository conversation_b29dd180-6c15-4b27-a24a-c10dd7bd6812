logging:
  level:
    ROOT: INFO
spring:
  application:
    name: integrations-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: DO_NOT_COMMIT
        database: integrations
  r2dbc:
    url: <connection_url>
    username: <username>
    password: <password>
server:
  port: 8080

vault:
  uri: "http://127.0.0.1:8200"
  token: DO_NOT_COMMIT
  namespace: admin

launch-darkly:
  sdk-key: DO_NOT_COMMIT

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

integrations-manager:
  integrations-config:
    wiz:
      name: Wiz
      description: Ingest cloud security issues and vulnerabilities from Wiz
      api-host: wiz.io
    armis:
      name: Armis
      description: Import Armis IoT/OT inventory and asset info to Illumio
      api-host: armis.com
    checkpoint:
      name: Checkpoint
      description: Ingest Checkpoint firewall logs for Insights
    kafka-light-task-producer-config:
      bootstrapServers: <server>
      saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT_";
      isConnectionString: true
      topic: light-task-queue

    kafka-heavy-task-producer-config:
      bootstrapServers: <server>
      saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT_";
      topic: heavy-task-queue
      isConnectionString: true
census:
  auth:
    enabled: true
grpc:
  channel:
    host: DO_NOT_COMMIT
    port: 443
    enableMtls: false
    caCert:
    mtlsKey:
    mtlsCert:
  server:
    port: 9090
