logging:
  level:
    ROOT: DEBUG
spring:
  application:
    name: integrations-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  # Cosmos no SQL Database Config
  r2dbc:
    url: r2dbc:postgresql://c-integrationstest.mmtdd3hnha22tf.postgres.cosmos.azure.com:5432/integrations?sslmode=require
    username: citus
    password: DO_NOT_COMMIT
    properties:
      sslMode: require
  data:
    r2dbc:
      repositories:
        enabled: true
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: DO_NOT_COMMIT
        database: integrations
server:
  port: 8081

vault:
  uri: "http://127.0.0.1:8200"
  token: DO_NOT_COMMIT

census:
  auth:
    enabled: false

launch-darkly:
  sdk-key: DO_NOT_COMMIT

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

integrations-manager:
  integrations-config:
    wiz:
      name: Wiz
      description: Ingest cloud security issues and vulnerabilities from Wiz
      api-host: wiz.io
    armis:
      name: Armis
      description: Import Armis IoT/OT inventory and asset info to Illumio
      api-host: armis.com
    checkpoint:
      name: Checkpoint
      description: Ingest Checkpoint firewall logs for Insights

  kafka-light-task-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    topic: task_queue
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true

  kafka-heavy-task-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    topic: test-integrations-heavy-tasks
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true