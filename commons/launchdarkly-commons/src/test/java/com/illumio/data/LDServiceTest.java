package com.illumio.data;


import com.illumio.data.components.LDService;
import com.illumio.data.model.Integration;
import com.launchdarkly.sdk.LDContext;
import com.launchdarkly.sdk.server.integrations.reactor.LDReactorClient;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.illumio.data.util.LaunchDarklyCommonConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LDServiceTest {

    @Mock private LDReactorClient ldReactorClient;

    private LDService ldService;

    private static final String TENANT_ID = "tenant-1";

    @BeforeEach
    void setUp() {
        ldService = new LDService(UUID.randomUUID().toString(), ldReactorClient);
    }

    @Test
    void integrationIsEnabledForTenant_Wiz_identifiesEnabled_Success() {
        when(ldReactorClient.boolVariation(any(), any(), anyBoolean())).thenReturn(Mono.just(true));

        StepVerifier.create(ldService.integrationIsEnabledForTenant(TENANT_ID, Integration.WIZ))
                    .expectNext(true)
                    .verifyComplete();

        ArgumentCaptor<LDContext> contextArgumentCaptor = ArgumentCaptor.forClass(LDContext.class);
        verify(ldReactorClient).boolVariation(eq("integration-wiz-enabled"), contextArgumentCaptor.capture(), eq(false));

        final LDContext capturedContext = contextArgumentCaptor.getValue();
        assertEquals(TENANT_ID, capturedContext.getValue(LD_FLAG_ATTRIBUTE_TENANT).stringValue());
        assertTrue(capturedContext.isAnonymous());
    }

    @Test
    void integrationIsEnabledForTenant_Armis_identifiesEnabled_Success() {
        when(ldReactorClient.boolVariation(any(), any(), anyBoolean())).thenReturn(Mono.just(true));

        StepVerifier.create(ldService.integrationIsEnabledForTenant(TENANT_ID, Integration.ARMIS))
                    .expectNext(true)
                    .verifyComplete();

        ArgumentCaptor<LDContext> contextArgumentCaptor = ArgumentCaptor.forClass(LDContext.class);
        verify(ldReactorClient).boolVariation(eq("integration-armis-enabled"), contextArgumentCaptor.capture(), eq(false));

        final LDContext capturedContext = contextArgumentCaptor.getValue();
        assertEquals(TENANT_ID, capturedContext.getValue(LD_FLAG_ATTRIBUTE_TENANT).stringValue());
        assertTrue(capturedContext.isAnonymous());
    }

    @Test
    void integrationIsEnabledForTenant_identifiesNotEnabled_Success() {
        when(ldReactorClient.boolVariation(any(), any(), anyBoolean())).thenReturn(Mono.just(false));

        StepVerifier.create(ldService.integrationIsEnabledForTenant(TENANT_ID, Integration.WIZ))
                    .expectNext(false)
                    .verifyComplete();

        ArgumentCaptor<LDContext> contextArgumentCaptor = ArgumentCaptor.forClass(LDContext.class);
        verify(ldReactorClient).boolVariation(eq("integration-wiz-enabled"), contextArgumentCaptor.capture(), eq(false));

        final LDContext capturedContext = contextArgumentCaptor.getValue();
        assertEquals(TENANT_ID, capturedContext.getValue(LD_FLAG_ATTRIBUTE_TENANT).stringValue());
        assertTrue(capturedContext.isAnonymous());
    }

    @Test
    void integrationIsEnabledForTenant_propagatesError() {
        when(ldReactorClient.boolVariation(any(), any(), anyBoolean())).thenReturn(Mono.error(new RuntimeException("LD issue")));

        StepVerifier.create(ldService.integrationIsEnabledForTenant(TENANT_ID, Integration.WIZ))
                    .expectError(RuntimeException.class)
                    .verify();
    }

}
