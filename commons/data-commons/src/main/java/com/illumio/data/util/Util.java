package com.illumio.data.util;

import com.illumio.data.model.Integration;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.wiz.WizAwaitingAsyncStatusMessage;
import com.illumio.data.model.wiz.WizReadyAsyncStatusMessage;
import com.illumio.data.model.wiz.WizReportIdentifier;
import com.illumio.data.model.wiz.WizSuccessStatusMessage;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;


import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HexFormat;
import java.util.List;


@Slf4j
@UtilityClass
public class Util {

    @SneakyThrows
    public String generateNewTaskId(final String tenantId, final Integration integration, final String scheduledTime) {
        String combinedKey = tenantId + "-" + integration + "-" + scheduledTime;
        return generateSha256Hash(combinedKey);
    }

    @SneakyThrows
    public String generateSha256Hash(final String input) {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
        return HexFormat.of().formatHex(hashBytes);
    }


    @SneakyThrows
    public String serializeWizAwaitingAsyncStatusMessage(final List<WizReportIdentifier> reportIdentifiers, final ObjectMapper objectMapper) {
        WizAwaitingAsyncStatusMessage wizAwaitingAsyncStatusMessage =
                WizAwaitingAsyncStatusMessage.builder()
                                             .reportIdentifiers(reportIdentifiers)
                                             .build();
        return objectMapper.writeValueAsString(wizAwaitingAsyncStatusMessage);
    }

    @SneakyThrows
    public String serializeWizReadyAsyncStatusMessage(final List<WizReportIdentifier> reportIdentifiers,
                                                      final ObjectMapper objectMapper) {
        WizReadyAsyncStatusMessage wizReadyAsyncStatusMessage =
                WizReadyAsyncStatusMessage.builder()
                                          .reportIdentifiers(reportIdentifiers)
                                          .build();
        return objectMapper.writeValueAsString(wizReadyAsyncStatusMessage);
    }

    @SneakyThrows
    public String serializeWizSuccessStatusMessage(final Long vulnerabilitiesUpdated,
                                                   final Long issuesUpdated,
                                                   final ObjectMapper objectMapper) {
        WizSuccessStatusMessage wizSuccessStatusMessage =
                WizSuccessStatusMessage.builder()
                                       .vulnerabilitiesUpdated(vulnerabilitiesUpdated)
                                       .issuesUpdated(issuesUpdated)
                                       .build();
        return objectMapper.writeValueAsString(wizSuccessStatusMessage);
    }

}