package com.illumio.data.model;

public abstract class TenantMetadata {

    public static Class<? extends TenantMetadata> getTenantMetadataClass(final Integration integration) {
        return switch (integration) {
            case WIZ -> WizTenantMetadata.class;
            case ARMIS -> EmptyTenantMetadata.class;
            case CHECKPOINT -> throw new UnsupportedOperationException("Checkpoint is not yet fully integrated in the framework");
        };
    }

}