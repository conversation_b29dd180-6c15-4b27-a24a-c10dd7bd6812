package com.illumio.data.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ObjectMapperConfig {

    /**
     * Creates and configures the Jackson ObjectMapper bean.
     * Reuses the same ObjectMapper instance rather than creating new instances throughout the application
     *
     * @return a configured ObjectMapper instance for JSON processing
     */
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }
}
